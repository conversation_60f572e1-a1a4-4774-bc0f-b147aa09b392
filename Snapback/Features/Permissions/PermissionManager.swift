import AppKit
import ApplicationServices  // For AXIsProcessTrusted
import Foundation

/// Manages accessibility permissions for the app
class PermissionManager: ObservableObject {
    static let shared = PermissionManager()

    /// Published property to track if accessibility permissions are granted
    @Published var isAccessibilityPermissionGranted: Bool = false

    /// Timer to periodically check permissions
    private var permissionCheckTimer: Timer?

    /// Notification name for permission changes
    static let permissionStatusChanged = Notification.Name("PermissionStatusChanged")

    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "PermissionManager"

    private init() {
        logger.debug("PermissionManager initializing", service: serviceName)

        // Check initial permission status
        isAccessibilityPermissionGranted = checkAccessibilityPermission()
        logger.info(
            "Initial permission status: \(isAccessibilityPermissionGranted)", service: serviceName)

        // Set up timer to check permissions periodically
        // Check every 2 seconds to reduce system load while still being responsive
        permissionCheckTimer = Timer.scheduledTimer(
            timeInterval: 2.0,
            target: self,
            selector: #selector(checkPermissionStatus),
            userInfo: nil,
            repeats: true
        )

        logger.debug("PermissionManager initialized with timer", service: serviceName)
    }

    /// Check if accessibility permissions are granted
    /// Uses a more reliable method than just AXIsProcessTrusted() by actually testing accessibility operations
    func checkAccessibilityPermission() -> Bool {
        // First check AXIsProcessTrusted() as a quick filter
        guard AXIsProcessTrusted() else {
            logger.debug("AXIsProcessTrusted() returned false", service: serviceName)
            return false
        }

        // AXIsProcessTrusted() can return true even when permissions are revoked
        // So we need to test actual accessibility operations to be sure
        return testActualAccessibilityOperation()
    }

    /// Test if we can actually perform accessibility operations
    /// This is more reliable than AXIsProcessTrusted() alone
    private func testActualAccessibilityOperation() -> Bool {
        // Try to get the frontmost application and its windows
        // This will fail if accessibility permissions are actually revoked
        guard let frontmostApp = NSWorkspace.shared.frontmostApplication else {
            logger.debug("No frontmost application found", service: serviceName)
            return false
        }

        let pid = frontmostApp.processIdentifier
        let appElement = AXUIElementCreateApplication(pid)

        // Try to get the windows attribute - this requires actual accessibility permissions
        var windowsRef: CFTypeRef?
        let error = AXUIElementCopyAttributeValue(
            appElement, kAXWindowsAttribute as CFString, &windowsRef)

        let hasPermissions = (error == .success)
        logger.debug(
            "Accessibility operation test result: \(hasPermissions ? "SUCCESS" : "FAILED") (error: \(error))",
            service: serviceName
        )

        return hasPermissions
    }

    /// Request accessibility permissions
    func requestAccessibilityPermission() {
        let alert = NSAlert()
        alert.messageText = "Accessibility Permissions Required"
        alert.informativeText =
            "Snapback needs Accessibility permissions to manage window positions. Please grant access in System Settings > Privacy & Security > Accessibility."
        alert.alertStyle = .warning
        alert.addButton(withTitle: "Open Settings")
        alert.addButton(withTitle: "Cancel")

        if alert.runModal() == .alertFirstButtonReturn {
            if let url = URL(
                string:
                    "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")
            {
                NSWorkspace.shared.open(url)
            }
        }
    }

    /// Periodically check permission status
    @objc private func checkPermissionStatus() {
        let currentStatus = checkAccessibilityPermission()

        // Log the current check for debugging
        logger.debug(
            "Permission check: current=\(currentStatus), stored=\(isAccessibilityPermissionGranted)",
            service: serviceName
        )

        // If permission status has changed, update and notify
        if currentStatus != isAccessibilityPermissionGranted {
            logger.info(
                "Permission status changed from \(isAccessibilityPermissionGranted) to \(currentStatus)",
                service: serviceName
            )

            isAccessibilityPermissionGranted = currentStatus

            // Post notification about permission change
            logger.debug("Posting permission status change notification", service: serviceName)
            NotificationCenter.default.post(
                name: PermissionManager.permissionStatusChanged,
                object: isAccessibilityPermissionGranted
            )
            logger.debug("Permission status change notification posted", service: serviceName)
        }
    }

    /// Force an immediate permission check (useful for testing or manual refresh)
    func forcePermissionCheck() {
        logger.debug("Force permission check requested", service: serviceName)
        checkPermissionStatus()
    }

    deinit {
        permissionCheckTimer?.invalidate()
        logger.debug("PermissionManager deinitialized", service: serviceName)
    }
}
