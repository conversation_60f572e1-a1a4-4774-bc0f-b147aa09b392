import AppKit
import ApplicationServices  // For AXIsProcessTrusted
import Foundation

/// Manages accessibility permissions for the app
class PermissionManager: ObservableObject {
    static let shared = PermissionManager()

    /// Published property to track if accessibility permissions are granted
    @Published var isAccessibilityPermissionGranted: Bool = false

    /// Timer to periodically check permissions
    private var permissionCheckTimer: Timer?

    /// Notification name for permission changes
    static let permissionStatusChanged = Notification.Name("PermissionStatusChanged")

    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "PermissionManager"

    private init() {
        logger.debug("PermissionManager initializing", service: serviceName)

        // Check initial permission status
        isAccessibilityPermissionGranted = checkAccessibilityPermission()
        logger.info(
            "Initial permission status: \(isAccessibilityPermissionGranted)", service: serviceName)

        // Set up timer to check permissions periodically
        // Check every 2 seconds to reduce system load while still being responsive
        permissionCheckTimer = Timer.scheduledTimer(
            timeInterval: 2.0,
            target: self,
            selector: #selector(checkPermissionStatus),
            userInfo: nil,
            repeats: true
        )

        logger.debug("PermissionManager initialized with timer", service: serviceName)
    }

    /// Check if accessibility permissions are granted
    /// Uses a more robust approach that combines AXIsProcessTrusted() with actual accessibility operations
    func checkAccessibilityPermission() -> Bool {
        let axTrustedResult = AXIsProcessTrusted()
        logger.debug("AXIsProcessTrusted() returned: \(axTrustedResult)", service: serviceName)

        // If AXIsProcessTrusted() returns false, try the actual operation test as a fallback
        // This handles cases where AXIsProcessTrusted() is slow to update
        if !axTrustedResult {
            logger.debug(
                "AXIsProcessTrusted() is false, testing actual accessibility operation as fallback",
                service: serviceName)
            let actualOperationResult = testActualAccessibilityOperation()
            logger.debug(
                "Actual accessibility operation test result: \(actualOperationResult)",
                service: serviceName)

            if actualOperationResult {
                logger.info(
                    "⚠️ AXIsProcessTrusted() returned false but actual operations work - using actual result",
                    service: serviceName)
                return true
            } else {
                logger.debug(
                    "Both AXIsProcessTrusted() and actual operations failed - permissions not granted",
                    service: serviceName)
                return false
            }
        }

        // If AXIsProcessTrusted() returns true, double-check with actual operations
        // This handles cases where AXIsProcessTrusted() returns true but permissions are actually revoked
        let actualOperationResult = testActualAccessibilityOperation()
        logger.debug(
            "AXIsProcessTrusted() is true, verifying with actual operation: \(actualOperationResult)",
            service: serviceName)

        if !actualOperationResult {
            logger.info(
                "⚠️ AXIsProcessTrusted() returned true but actual operations fail - permissions likely revoked",
                service: serviceName)
            return false
        }

        logger.debug(
            "Both AXIsProcessTrusted() and actual operations succeeded - permissions granted",
            service: serviceName)
        return true
    }

    /// Test if we can actually perform accessibility operations
    /// This is more reliable than AXIsProcessTrusted() alone
    private func testActualAccessibilityOperation() -> Bool {
        logger.debug("🔍 Starting actual accessibility operation test", service: serviceName)

        // Try to get the frontmost application and its windows
        // This will fail if accessibility permissions are actually revoked
        guard let frontmostApp = NSWorkspace.shared.frontmostApplication else {
            logger.debug("❌ No frontmost application found", service: serviceName)
            return false
        }

        let appName = frontmostApp.localizedName ?? "Unknown"
        let pid = frontmostApp.processIdentifier
        logger.debug(
            "🎯 Testing accessibility with frontmost app: \(appName) (PID: \(pid))",
            service: serviceName)

        let appElement = AXUIElementCreateApplication(pid)

        // Try to get the windows attribute - this requires actual accessibility permissions
        var windowsRef: CFTypeRef?
        let error = AXUIElementCopyAttributeValue(
            appElement, kAXWindowsAttribute as CFString, &windowsRef)

        let hasPermissions = (error == .success)

        if hasPermissions {
            let windowCount = (windowsRef as? [AXUIElement])?.count ?? 0
            logger.debug(
                "✅ Accessibility operation SUCCESS - found \(windowCount) windows for \(appName)",
                service: serviceName
            )
        } else {
            logger.debug(
                "❌ Accessibility operation FAILED for \(appName) - error: \(error) (\(error.rawValue))",
                service: serviceName
            )
        }

        return hasPermissions
    }

    /// Request accessibility permissions
    func requestAccessibilityPermission() {
        let alert = NSAlert()
        alert.messageText = "Accessibility Permissions Required"
        alert.informativeText =
            "Snapback needs Accessibility permissions to manage window positions. Please grant access in System Settings > Privacy & Security > Accessibility."
        alert.alertStyle = .warning
        alert.addButton(withTitle: "Open Settings")
        alert.addButton(withTitle: "Cancel")

        if alert.runModal() == .alertFirstButtonReturn {
            if let url = URL(
                string:
                    "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")
            {
                NSWorkspace.shared.open(url)
            }
        }
    }

    /// Periodically check permission status
    @objc private func checkPermissionStatus() {
        let currentStatus = checkAccessibilityPermission()

        // Log the current check for debugging
        logger.debug(
            "Permission check: current=\(currentStatus), stored=\(isAccessibilityPermissionGranted)",
            service: serviceName
        )

        // If permission status has changed, update and notify
        if currentStatus != isAccessibilityPermissionGranted {
            logger.info(
                "Permission status changed from \(isAccessibilityPermissionGranted) to \(currentStatus)",
                service: serviceName
            )

            isAccessibilityPermissionGranted = currentStatus

            // Post notification about permission change
            logger.debug("Posting permission status change notification", service: serviceName)
            NotificationCenter.default.post(
                name: PermissionManager.permissionStatusChanged,
                object: isAccessibilityPermissionGranted
            )
            logger.debug("Permission status change notification posted", service: serviceName)
        }
    }

    /// Force an immediate permission check (useful for testing or manual refresh)
    func forcePermissionCheck() {
        logger.info("🔄 Force permission check requested", service: serviceName)
        checkPermissionStatus()
    }

    /// Force a comprehensive permission check with detailed logging
    /// This method provides more detailed feedback for debugging permission issues
    func forceComprehensivePermissionCheck() {
        logger.info("🔍 Comprehensive permission check requested", service: serviceName)

        // Log current state
        logger.info(
            "Current stored permission state: \(isAccessibilityPermissionGranted)",
            service: serviceName)

        // Test AXIsProcessTrusted directly
        let axResult = AXIsProcessTrusted()
        logger.info("Direct AXIsProcessTrusted() call: \(axResult)", service: serviceName)

        // Test actual accessibility operation
        let actualResult = testActualAccessibilityOperation()
        logger.info("Direct accessibility operation test: \(actualResult)", service: serviceName)

        // Run the full check
        let fullCheckResult = checkAccessibilityPermission()
        logger.info(
            "Full checkAccessibilityPermission() result: \(fullCheckResult)", service: serviceName)

        // Force update the status
        checkPermissionStatus()

        logger.info("Comprehensive permission check completed", service: serviceName)
    }

    deinit {
        permissionCheckTimer?.invalidate()
        logger.debug("PermissionManager deinitialized", service: serviceName)
    }
}
