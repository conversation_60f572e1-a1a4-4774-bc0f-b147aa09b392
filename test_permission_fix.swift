#!/usr/bin/env swift

import Foundation
import ApplicationServices

// Simple test to verify our permission detection logic
func testPermissionDetection() {
    print("🔍 Testing Permission Detection Logic")
    print("=====================================")
    
    // Test 1: Basic AXIsProcessTrusted check
    let basicCheck = AXIsProcessTrusted()
    print("1. AXIsProcessTrusted(): \(basicCheck)")
    
    // Test 2: Try actual accessibility operation (like our new implementation)
    let actualCheck = testActualAccessibilityOperation()
    print("2. Actual accessibility operation test: \(actualCheck)")
    
    // Test 3: Compare results
    if basicCheck != actualCheck {
        print("⚠️  MISMATCH DETECTED!")
        print("   AXIsProcessTrusted() says: \(basicCheck)")
        print("   Actual operation test says: \(actualCheck)")
        print("   This confirms the bug - AXIsProcessTrusted() is unreliable!")
    } else {
        print("✅ Both checks agree: \(basicCheck)")
    }
    
    print("\n🎯 Recommended permission status: \(actualCheck)")
    print("   (This is what <PERSON>nap<PERSON> should use)")
}

func testActualAccessibilityOperation() -> Bool {
    // Try to get the frontmost application and its windows
    guard let frontmostApp = NSWorkspace.shared.frontmostApplication else {
        print("   No frontmost application found")
        return false
    }
    
    let pid = frontmostApp.processIdentifier
    let appElement = AXUIElementCreateApplication(pid)
    
    // Try to get the windows attribute - this requires actual accessibility permissions
    var windowsRef: CFTypeRef?
    let error = AXUIElementCopyAttributeValue(
        appElement, kAXWindowsAttribute as CFString, &windowsRef)
    
    let hasPermissions = (error == .success)
    print("   Frontmost app: \(frontmostApp.localizedName ?? "Unknown")")
    print("   Accessibility operation result: \(error)")
    
    return hasPermissions
}

// Run the test
testPermissionDetection()
